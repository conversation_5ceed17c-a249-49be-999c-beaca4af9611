# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=tmp.requirements.txt requirements/requirements.in
aiohappyeyeballs==2.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
aiohttp==3.11.18
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
aiosignal==1.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
annotated-types==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
anyio==4.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   httpx
    #   openai
    #   watchfiles
attrs==25.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   jsonschema
    #   referencing
backoff==2.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   posthog
beautifulsoup4==4.13.4
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
cachetools==5.5.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
certifi==2025.4.26
    # via
    #   -c requirements/common-constraints.txt
    #   httpcore
    #   httpx
    #   requests
cffi==1.17.1
    # via
    #   -c requirements/common-constraints.txt
    #   sounddevice
    #   soundfile
charset-normalizer==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   requests
click==8.1.8
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
configargparse==1.7.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
diff-match-patch==20241021
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
diskcache==5.6.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
distro==1.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   openai
    #   posthog
filelock==3.18.0
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
flake8==7.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
frozenlist==1.6.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
gitdb==4.0.12
    # via
    #   -c requirements/common-constraints.txt
    #   gitpython
gitpython==3.1.44
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
google-ai-generativelanguage==0.6.15
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
google-api-core[grpc]==2.24.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.169.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
google-auth==2.40.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-python-client
google-generativeai==0.8.5
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
googleapis-common-protos==1.70.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   grpcio-status
grep-ast==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
grpcio==1.71.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
h11==0.16.0
    # via
    #   -c requirements/common-constraints.txt
    #   httpcore
hf-xet==1.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   huggingface-hub
httpcore==1.0.9
    # via
    #   -c requirements/common-constraints.txt
    #   httpx
httplib2==0.22.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
    #   openai
huggingface-hub==0.31.1
    # via
    #   -c requirements/common-constraints.txt
    #   tokenizers
idna==3.10
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==7.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   litellm
importlib-resources==6.5.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
jinja2==3.1.6
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
jiter==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   openai
json5==0.12.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
jsonschema==4.23.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   litellm
jsonschema-specifications==2025.4.1
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
litellm==1.68.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
markdown-it-py==3.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   rich
markupsafe==3.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   jinja2
mccabe==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   flake8
mdurl==0.1.2
    # via
    #   -c requirements/common-constraints.txt
    #   markdown-it-py
mixpanel==4.10.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
mslex==1.3.0
    # via
    #   -c requirements/common-constraints.txt
    #   oslex
multidict==6.4.3
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   yarl
networkx==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
numpy==1.26.4
    # via
    #   -c requirements/common-constraints.txt
    #   scipy
    #   soundfile
openai==1.75.0
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
oslex==0.1.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
packaging==24.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   huggingface-hub
pathspec==0.12.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   grep-ast
pexpect==4.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
pillow==11.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
posthog==4.0.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
prompt-toolkit==3.0.51
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
propcache==0.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.4
    # via
    #   -c requirements/common-constraints.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psutil==7.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
ptyprocess==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   pexpect
pyasn1==0.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
pycodestyle==2.13.0
    # via
    #   -c requirements/common-constraints.txt
    #   flake8
pycparser==2.22
    # via
    #   -c requirements/common-constraints.txt
    #   cffi
pydantic==2.11.4
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
    #   litellm
    #   openai
pydantic-core==2.33.2
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
pydub==0.25.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
pyflakes==3.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   flake8
pygments==2.19.1
    # via
    #   -c requirements/common-constraints.txt
    #   rich
pypandoc==1.15
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
pyparsing==3.2.3
    # via
    #   -c requirements/common-constraints.txt
    #   httplib2
pyperclip==1.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
python-dateutil==2.9.0.post0
    # via
    #   -c requirements/common-constraints.txt
    #   posthog
python-dotenv==1.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
pyyaml==6.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
    #   huggingface-hub
referencing==0.36.2
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   -c requirements/common-constraints.txt
    #   tiktoken
requests==2.32.3
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   huggingface-hub
    #   mixpanel
    #   posthog
    #   tiktoken
rich==14.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
rpds-py==0.24.0
    # via
    #   -c requirements/common-constraints.txt
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
scipy==1.15.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
shtab==1.7.2
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
six==1.17.0
    # via
    #   -c requirements/common-constraints.txt
    #   mixpanel
    #   posthog
    #   python-dateutil
smmap==5.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   gitdb
sniffio==1.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
    #   openai
socksio==1.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
sounddevice==0.5.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
soundfile==0.13.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
soupsieve==2.7
    # via
    #   -c requirements/common-constraints.txt
    #   beautifulsoup4
tiktoken==0.9.0
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
tokenizers==0.21.1
    # via
    #   -c requirements/common-constraints.txt
    #   litellm
tqdm==4.67.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-generativeai
    #   huggingface-hub
    #   openai
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
tree-sitter-c-sharp==0.23.1
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
tree-sitter-embedded-template==0.23.2
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
tree-sitter-language-pack==0.7.3
    # via
    #   -c requirements/common-constraints.txt
    #   grep-ast
tree-sitter-yaml==0.7.0
    # via
    #   -c requirements/common-constraints.txt
    #   tree-sitter-language-pack
typing-extensions==4.13.2
    # via
    #   -c requirements/common-constraints.txt
    #   anyio
    #   beautifulsoup4
    #   google-generativeai
    #   huggingface-hub
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   typing-inspection
typing-inspection==0.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   pydantic
uritemplate==4.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-python-client
urllib3==2.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   mixpanel
    #   requests
watchfiles==1.0.5
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements.in
wcwidth==0.2.13
    # via
    #   -c requirements/common-constraints.txt
    #   prompt-toolkit
yarl==1.20.0
    # via
    #   -c requirements/common-constraints.txt
    #   aiohttp
zipp==3.21.0
    # via
    #   -c requirements/common-constraints.txt
    #   importlib-metadata
    
tree-sitter==0.23.2; python_version < "3.10"
tree-sitter==0.24.0; python_version >= "3.10"
